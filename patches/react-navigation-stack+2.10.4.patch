diff --git a/node_modules/react-navigation-stack/lib/commonjs/vendor/views/Header/HeaderBackButton.js b/node_modules/react-navigation-stack/lib/commonjs/vendor/views/Header/HeaderBackButton.js
index 5abd3c5..d5312eb 100644
--- a/node_modules/react-navigation-stack/lib/commonjs/vendor/views/Header/HeaderBackButton.js
+++ b/node_modules/react-navigation-stack/lib/commonjs/vendor/views/Header/HeaderBackButton.js
@@ -68,7 +68,7 @@ function HeaderBackButton({
         style: [styles.icon, Bo<PERSON>an(labelVisible) && styles.iconWithLabel, <PERSON><PERSON><PERSON>(tintColor) && {
           tintColor
         }],
-        source: require('../assets/back-icon.png'),
+        source: require('../../../../../../assets/back-icon.png'),
         fadeDuration: 0
       });
     }
@@ -109,7 +109,7 @@ function HeaderBackButton({
       maskElement: /*#__PURE__*/React.createElement(_reactNative.View, {
         style: styles.iconMaskContainer
       }, /*#__PURE__*/React.createElement(_reactNative.Image, {
-        source: require('../assets/back-icon-mask.png'),
+        source: require('../../../../../../assets/back-icon-mask.png'),
         style: styles.iconMask
       }), /*#__PURE__*/React.createElement(_reactNative.View, {
         style: styles.iconMaskFillerRect
