/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

const {getDefaultConfig} = require('metro-config');

module.exports = (async () => {
  const {
    resolver: {sourceExts, assetExts},
  } = await getDefaultConfig();

  return {
    transformer: {
      getTransformOptions: async () => ({
        transform: {
          experimentalImportSupport: false,
          inlineRequires: true,
        },
      }),
      // Fix for React Navigation v4 asset issues
      assetRegistryPath: 'react-native/Libraries/Image/AssetRegistry',
    },
    resolver: {
      assetExts: [...assetExts, 'bin', 'txt', 'jpg', 'png', 'json', 'gif', 'webp', 'svg'],
      sourceExts: [...sourceExts, 'js', 'jsx', 'ts', 'tsx'],
      // Allow resolving assets from node_modules
      platforms: ['ios', 'android', 'native', 'web'],
    },
    // Additional configuration for asset handling
    watchFolders: [],
    maxWorkers: 2,
  };
})();
